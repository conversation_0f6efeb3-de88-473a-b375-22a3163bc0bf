import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { X, Check, Minus } from "lucide-react"
import { useNavigate } from "react-router-dom"

interface CartItem {
  id: string
  qh: number
  description: string
  quantity: number
  price: number
  discount: number
  value: number
}

interface Product {
  id: string
  name: string
  price: number
  category: string
  subcategory?: string
}

const demoProducts: Product[] = [
  // A. Pipes
  { id: "1", name: "Glass Water Pipe", price: 25.99, category: "A. Pipes", subcategory: "Water Pipes" },
  { id: "2", name: "Ceramic Smoking Pipe", price: 15.50, category: "A. Pipes", subcategory: "Dry Pipes" },
  { id: "3", name: "Metal Hand Pipe", price: 12.99, category: "<PERSON>. <PERSON>", subcategory: "Metal Pipes" },
  { id: "4", name: "Silicone Bong", price: 35.00, category: "A. Pipes", subcategory: "Water Pipes" },
  { id: "5", name: "Wooden Pipe", price: 18.75, category: "A. Pipes", subcategory: "Dry Pipes" },

  // A. Deli
  { id: "6", name: "Turkey Sandwich", price: 8.99, category: "A. Deli", subcategory: "Sandwiches" },
  { id: "7", name: "Ham & Cheese", price: 7.50, category: "A. Deli", subcategory: "Sandwiches" },
  { id: "8", name: "Chicken Salad", price: 6.25, category: "A. Deli", subcategory: "Salads" },
  { id: "9", name: "Tuna Melt", price: 9.25, category: "A. Deli", subcategory: "Hot Items" },
  { id: "10", name: "Caesar Salad", price: 7.99, category: "A. Deli", subcategory: "Salads" },

  // A. Pills
  { id: "11", name: "Vitamin C 1000mg", price: 12.99, category: "A. Pills", subcategory: "Vitamins" },
  { id: "12", name: "Multivitamin", price: 15.50, category: "A. Pills", subcategory: "Vitamins" },
  { id: "13", name: "Aspirin 325mg", price: 8.99, category: "A. Pills", subcategory: "Pain Relief" },
  { id: "14", name: "Ibuprofen 200mg", price: 9.75, category: "A. Pills", subcategory: "Pain Relief" },
  { id: "15", name: "Calcium Supplement", price: 11.25, category: "A. Pills", subcategory: "Supplements" },

  // B. Nailpolish Remo
  { id: "16", name: "Acetone Remover", price: 4.99, category: "B. Nailpolish Remo", subcategory: "Removers" },
  { id: "17", name: "Non-Acetone Remover", price: 5.50, category: "B. Nailpolish Remo", subcategory: "Removers" },
  { id: "18", name: "Nail Polish Remover Pads", price: 6.25, category: "B. Nailpolish Remo", subcategory: "Pads" },
  { id: "19", name: "Cuticle Remover", price: 7.99, category: "B. Nailpolish Remo", subcategory: "Cuticle Care" },
  { id: "20", name: "Gel Polish Remover", price: 8.75, category: "B. Nailpolish Remo", subcategory: "Gel Removers" },

  // Black Unicorn
  { id: "21", name: "Black Unicorn Energy Drink", price: 3.99, category: "Black Unicorn", subcategory: "Energy Drinks" },
  { id: "22", name: "Black Unicorn Protein Bar", price: 4.50, category: "Black Unicorn", subcategory: "Protein" },
  { id: "23", name: "Black Unicorn Pre-Workout", price: 29.99, category: "Black Unicorn", subcategory: "Supplements" },
  { id: "24", name: "Black Unicorn Recovery Drink", price: 5.25, category: "Black Unicorn", subcategory: "Recovery" },
  { id: "25", name: "Black Unicorn BCAA", price: 24.99, category: "Black Unicorn", subcategory: "Supplements" },

  // C. Miscellaneous
  { id: "26", name: "Phone Charger", price: 12.99, category: "C. Miscellaneous", subcategory: "Electronics" },
  { id: "27", name: "Lighter", price: 1.99, category: "C. Miscellaneous", subcategory: "Accessories" },
  { id: "28", name: "Sunglasses", price: 15.99, category: "C. Miscellaneous", subcategory: "Accessories" },
  { id: "29", name: "Keychain", price: 3.50, category: "C. Miscellaneous", subcategory: "Accessories" },
  { id: "30", name: "Notebook", price: 4.99, category: "C. Miscellaneous", subcategory: "Stationery" },
  { id: "31", name: "Pen Set", price: 7.25, category: "C. Miscellaneous", subcategory: "Stationery" },
  { id: "32", name: "USB Cable", price: 8.99, category: "C. Miscellaneous", subcategory: "Electronics" },
]

export default function POSSystem() {
  const navigate = useNavigate()
  const [cartItems, setCartItems] = useState<CartItem[]>([])
  const [currentInput, setCurrentInput] = useState("")
  const [selectedItemIndex, setSelectedItemIndex] = useState(0)
  const [specialDiscount, setSpecialDiscount] = useState(false)
  const [discountAmount, setDiscountAmount] = useState(0)
  const [currentTime, setCurrentTime] = useState(new Date())
  const [showProductModal, setShowProductModal] = useState(false)
  const [showCheckoutModal, setShowCheckoutModal] = useState(false)
  const [isEditingPrice, setIsEditingPrice] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null)
  const [selectedSubcategory, setSelectedSubcategory] = useState<string | null>(null)

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)
    return () => clearInterval(timer)
  }, [])

  const handleNumberClick = (num: string) => {
    setCurrentInput((prev) => prev + num)
  }

  const handleClear = () => {
    setCurrentInput("")
    setIsEditingPrice(false)
  }

  const handleEnter = () => {
    if (currentInput && cartItems.length > 0) {
      if (isEditingPrice) {
        // Update price of selected item
        const newPrice = Number.parseFloat(currentInput)
        if (newPrice > 0 && selectedItemIndex < cartItems.length) {
          const updatedItems = [...cartItems]
          updatedItems[selectedItemIndex].price = newPrice
          updatedItems[selectedItemIndex].value = updatedItems[selectedItemIndex].quantity * newPrice
          setCartItems(updatedItems)
        }
        setIsEditingPrice(false)
      } else {
        // Update quantity of selected item
        const quantity = Number.parseInt(currentInput)
        if (quantity > 0 && selectedItemIndex < cartItems.length) {
          const updatedItems = [...cartItems]
          updatedItems[selectedItemIndex].quantity = quantity
          updatedItems[selectedItemIndex].value = quantity * updatedItems[selectedItemIndex].price
          setCartItems(updatedItems)
        }
      }
      setCurrentInput("")
    }
  }

  const handleAddItem = (product: Product) => {
    const existingItemIndex = cartItems.findIndex((item) => item.description === product.name)

    if (existingItemIndex >= 0) {
      // If item already exists, increase quantity
      const updatedItems = [...cartItems]
      updatedItems[existingItemIndex].quantity += 1
      updatedItems[existingItemIndex].value =
        updatedItems[existingItemIndex].quantity * updatedItems[existingItemIndex].price
      setCartItems(updatedItems)
    } else {
      // Add new item
      const newItem: CartItem = {
        id: Date.now().toString(),
        qh: cartItems.length,
        description: product.name,
        quantity: 1,
        price: product.price,
        discount: 0,
        value: product.price,
      }
      setCartItems([...cartItems, newItem])
    }
  }

  const handleRemoveItemFromModal = (product: Product) => {
    const existingItemIndex = cartItems.findIndex((item) => item.description === product.name)

    if (existingItemIndex >= 0) {
      const updatedItems = [...cartItems]
      if (updatedItems[existingItemIndex].quantity > 1) {
        // Decrease quantity
        updatedItems[existingItemIndex].quantity -= 1
        updatedItems[existingItemIndex].value =
          updatedItems[existingItemIndex].quantity * updatedItems[existingItemIndex].price
        setCartItems(updatedItems)
      } else {
        // Remove item completely
        updatedItems.splice(existingItemIndex, 1)
        setCartItems(updatedItems)
        if (selectedItemIndex >= updatedItems.length) {
          setSelectedItemIndex(Math.max(0, updatedItems.length - 1))
        }
      }
    }
  }

  const handleRemoveItem = (index: number) => {
    const updatedItems = cartItems.filter((_, i) => i !== index)
    setCartItems(updatedItems)
    if (selectedItemIndex >= updatedItems.length) {
      setSelectedItemIndex(Math.max(0, updatedItems.length - 1))
    }
  }

  const handleEditPrice = () => {
    if (cartItems.length > 0 && selectedItemIndex < cartItems.length) {
      setIsEditingPrice(true)
      setCurrentInput(cartItems[selectedItemIndex].price.toString())
    }
  }

  const getItemQuantityInCart = (productName: string) => {
    const item = cartItems.find((item) => item.description === productName)
    return item ? item.quantity : 0
  }

  const calculateSubtotal = () => {
    return cartItems.reduce((sum, item) => sum + item.value, 0)
  }

  const calculateTax = () => {
    return calculateSubtotal() * 0.06625
  }

  const calculateTotal = () => {
    return calculateSubtotal() + calculateTax() - discountAmount
  }

  const handleCheckout = () => {
    if (cartItems.length > 0) {
      setShowCheckoutModal(true)
    }
  }

  const handleCompleteCheckout = () => {
    setCartItems([])
    setCurrentInput("")
    setSelectedItemIndex(0)
    setDiscountAmount(0)
    setSpecialDiscount(false)
    setShowCheckoutModal(false)
  }

  const handleTheaterClick = () => {
    navigate("/theater")
  }

  const handleAdminClick = () => {
    navigate("/admin")
  }

  const formatCurrency = (amount: number) => {
    return amount.toFixed(2)
  }

  const formatDate = (date: Date) => {
    return date.toLocaleDateString("en-US", { month: "2-digit", day: "2-digit", year: "2-digit" })
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString("en-US", { hour12: false })
  }



  // Get unique categories
  const categories = Array.from(new Set(demoProducts.map(p => p.category)))

  // Get subcategories for selected category
  const getSubcategoriesForCategory = (category: string) => {
    return Array.from(new Set(
      demoProducts
        .filter(p => p.category === category && p.subcategory)
        .map(p => p.subcategory!)
    ))
  }

  // Get filtered products based on selection
  const getFilteredProducts = () => {
    if (!selectedCategory) return demoProducts

    let filtered = demoProducts.filter(p => p.category === selectedCategory)

    if (selectedSubcategory) {
      filtered = filtered.filter(p => p.subcategory === selectedSubcategory)
    }

    return filtered
  }

  return (
    <div className="h-screen bg-black text-green-400 p-2 flex flex-col overflow-hidden">
      {/* Header */}
      <div className="bg-black border border-gray-600 p-4 mb-2 flex-shrink-0">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-black text-green-400">POINT OF SALE SYSTEM</h1>
            <p className="text-xl font-bold text-green-300">Rainbow Station Inc.</p>
          </div>
          <div className="flex items-center gap-4">
            <div className="text-right">
              <div className="text-lg font-bold">
                Date: <span className="bg-gray-700 px-3 py-2 text-xl font-black">{formatDate(currentTime)}</span>
              </div>
              <div className="text-lg font-bold">
                Time: <span className="bg-gray-700 px-3 py-2 text-xl font-black">{formatTime(currentTime)}</span>
              </div>
              <div className="text-lg font-bold">
                Operator: <span className="bg-gray-700 px-3 py-2 text-xl font-black">Simon</span>
              </div>
            </div>
            <Button
              onClick={handleAdminClick}
              className="bg-black text-red-500 border border-green-400 text-xl font-black px-6 py-3"
            >
              ADMIN
            </Button>
            <Button
              onClick={handleTheaterClick}
              className="bg-black text-red-500 border border-green-400 text-xl font-black px-6 py-3"
            >
              THEATER
            </Button>
          </div>
        </div>
      </div>

      <div className="flex gap-2 flex-1 overflow-hidden">
        {/* Main Content */}
        <div className="flex-1 flex flex-col max-w-[calc(100vw-400px)]">
          {/* Transaction Table */}
          <div className="bg-black border border-gray-600 mb-2 flex-1 overflow-y-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-600 sticky top-0 bg-black">
                  <th className="text-red-500 p-3 text-left text-xl font-black">QH</th>
                  <th className="text-red-500 p-3 text-left text-xl font-black">Description</th>
                  <th className="text-red-500 p-3 text-left text-xl font-black">Quantity</th>
                  <th className="text-red-500 p-3 text-left text-xl font-black">Price</th>
                  <th className="text-red-500 p-3 text-left text-xl font-black">Disc</th>
                  <th className="text-red-500 p-3 text-left text-xl font-black">Value</th>
                </tr>
              </thead>
              <tbody>
                {cartItems.map((item, index) => (
                  <tr
                    key={item.id}
                    className={`border-b border-gray-700 cursor-pointer ${selectedItemIndex === index ? "bg-gray-800" : ""}`}
                    onClick={() => setSelectedItemIndex(index)}
                  >
                    <td className="p-3 text-green-400 text-xl font-bold">{index}</td>
                    <td className="p-3 text-green-400 text-xl font-bold">{item.description}</td>
                    <td className="p-3 text-green-400 text-xl font-bold">{item.quantity}</td>
                    <td className="p-3 text-green-400 text-xl font-bold">{formatCurrency(item.price)}</td>
                    <td className="p-3 text-green-400 text-xl font-bold">{formatCurrency(item.discount)}</td>
                    <td className="p-3 text-green-400 text-xl font-bold">{formatCurrency(item.value)}</td>
                  </tr>
                ))}
                {cartItems.length === 0 && (
                  <tr>
                    <td colSpan={6} className="p-8 text-center text-gray-500 text-xl">
                      No items in cart. Click SELECT ITEM to add products.
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>

          {/* Bottom Sections Container */}
          <div className="mt-auto space-y-3">
            {/* Bottom Section */}
            <div className="flex flex-col lg:flex-row gap-4 lg:gap-6">
              {/* Discount Section */}
              <div className="flex-1 bg-gray-900 border border-gray-600 rounded-lg p-4">
                <Button className="bg-black text-red-500 border border-red-400 mb-3 text-lg sm:text-xl font-black px-4 sm:px-6 py-2 w-full">
                  Add Discount
                </Button>
                <div className="flex items-center gap-3 mt-2">
                  <Checkbox checked={specialDiscount} onCheckedChange={setSpecialDiscount} className="border-green-400 w-5 h-5" />
                  <span className="text-green-400 text-base sm:text-lg font-bold">Special Discount</span>
                </div>
              </div>

              {/* Totals */}
              <div className="flex-1 bg-gray-900 border border-gray-600 rounded-lg p-4">
                <div className="space-y-2">
                  <div className="flex justify-between items-center py-1">
                    <span className="text-green-400 text-base sm:text-lg font-bold">Total Item Discount</span>
                    <span className="text-green-400 text-base sm:text-lg font-black bg-gray-800 px-3 py-1 rounded">{formatCurrency(0.0)}</span>
                  </div>
                  <div className="flex justify-between items-center py-1">
                    <span className="text-green-400 text-base sm:text-lg font-bold">Discount</span>
                    <span className="text-green-400 text-base sm:text-lg font-black bg-gray-800 px-3 py-1 rounded">{formatCurrency(discountAmount)}</span>
                  </div>
                  <div className="flex justify-between items-center py-1 border-t border-gray-600 pt-2">
                    <span className="text-green-400 text-base sm:text-lg font-bold">Sub total</span>
                    <span className="text-green-400 text-base sm:text-lg font-black bg-gray-800 px-3 py-1 rounded">{formatCurrency(calculateSubtotal())}</span>
                  </div>
                  <div className="flex justify-between items-center py-1">
                    <span className="text-green-400 text-base sm:text-lg font-bold">Tax 6.625%</span>
                    <span className="text-green-400 text-base sm:text-lg font-black bg-gray-800 px-3 py-1 rounded">{formatCurrency(calculateTax())}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Payment Section */}
            <div className="flex gap-3">
              {/* Item Count */}
              <div className="bg-black border-2 border-gray-600 p-6 text-center min-w-[100px] rounded-lg">
                <div className="text-7xl font-black text-green-400">{cartItems.length}</div>
                <div className="text-sm text-green-300 mt-1">ITEMS</div>
              </div>

              {/* Cash and Change */}
              <div className="bg-black border-2 border-gray-600 p-6 flex-1 rounded-lg">
                <div className="h-full flex flex-col justify-center space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-green-400 text-2xl font-bold">Cash</span>
                    <div className="text-4xl font-black text-green-400 bg-gray-800 px-6 py-3 rounded-lg min-w-[140px] text-center">
                      {formatCurrency(calculateTotal())}
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-green-400 text-2xl font-bold">Change</span>
                    <div className="text-4xl font-black text-green-400 bg-gray-800 px-6 py-3 rounded-lg min-w-[140px] text-center">
                      0.00
                    </div>
                  </div>
                </div>
              </div>

              {/* Total Amount */}
              <div className="bg-black border-2 border-gray-600 p-6 text-center min-w-[220px] rounded-lg flex flex-col justify-center">
                <div className="text-lg text-green-300 mb-3 font-bold">TOTAL</div>
                <div className="text-8xl font-black text-green-400 leading-none">{formatCurrency(calculateTotal())}</div>
              </div>

              {/* Checkout Button */}
              <div className="bg-black border-2 border-gray-600 p-6 min-w-[140px] rounded-lg">
                <Button
                  onClick={handleCheckout}
                  className="bg-black text-red-500 border-2 border-red-400 w-full h-full text-2xl font-black hover:bg-red-900 rounded-lg"
                >
                  <div className="flex flex-col items-center">
                    <span>CHECK OUT</span>
                    <span className="text-3xl">🛒</span>
                  </div>
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Right Panel - Keypad and Controls */}
        <div className="w-96 flex flex-col h-full">
          {/* Display */}
          <div className="bg-black border border-gray-600 p-3 mb-1 h-14">
            <div className="text-2xl font-black text-green-400 text-center">
              {currentInput || "0"}
              {isEditingPrice && <span className="text-red-500 text-sm ml-2">(PRICE)</span>}
            </div>
          </div>

          {/* Keypad */}
          <div className="grid grid-cols-3 gap-1 mb-2">
            {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((num) => (
              <Button
                key={num}
                onClick={() => handleNumberClick(num.toString())}
                className="bg-black border-2 border-green-400 text-green-400 text-3xl font-black h-14 hover:bg-gray-900"
              >
                {num}
              </Button>
            ))}
            <Button
              onClick={() => handleNumberClick("00")}
              className="bg-black border-2 border-green-400 text-green-400 text-3xl font-black h-14 hover:bg-gray-900"
            >
              00
            </Button>
            <Button
              onClick={() => handleNumberClick("0")}
              className="bg-black border-2 border-green-400 text-green-400 text-3xl font-black h-14 hover:bg-gray-900"
            >
              0
            </Button>
            <Button
              onClick={() => setCurrentInput((prev) => prev.slice(0, -1))}
              className="bg-black border-2 border-green-400 text-green-400 text-3xl font-black h-14 hover:bg-gray-900"
            >
              ←
            </Button>
          </div>

          {/* Control Buttons */}
          <div className="grid grid-cols-2 gap-1 mb-2">
            <Button
              onClick={handleClear}
              className="bg-black border-2 border-green-400 text-green-400 text-xl font-black h-14 hover:bg-gray-900"
            >
              CLEAR
            </Button>
            <Button
              onClick={handleEnter}
              className="bg-black border-2 border-green-400 text-green-400 text-xl font-black h-14 hover:bg-gray-900"
            >
              ENTER
            </Button>
          </div>

          <Button className="bg-black text-red-500 border-2 border-red-400 text-xl font-black h-14 w-full mb-2">
            RETURN/EXCHANGE
          </Button>

          {/* Function Buttons */}
          <div className="grid grid-cols-3 gap-1 flex-1 grid-rows-2 h-full">
            <Button
              onClick={() => {
                setSelectedCategory(null)
                setSelectedSubcategory(null)
                setShowProductModal(true)
              }}
              className="bg-black text-red-500 border border-red-400 h-full text-lg font-black flex flex-col items-center justify-center"
            >
              <span>SELECT</span>
              <span>ITEM</span>
            </Button>
            <Button className="bg-black text-blue-400 border border-blue-400 h-full text-lg font-black">HOLD</Button>
            <Button className="bg-black text-blue-400 border border-blue-400 h-full text-lg font-black">RECALL</Button>
            <Button
              onClick={handleEditPrice}
              className="bg-black text-red-500 border border-red-400 h-full text-lg font-black flex flex-col items-center justify-center"
            >
              <span>EDIT</span>
              <span>PRICE</span>
            </Button>
            <Button className="bg-black text-red-500 border border-red-400 h-full text-lg font-black flex flex-col items-center justify-center">
              <span>OPEN</span>
              <span>DRAWER</span>
            </Button>
            <Button
              onClick={() => handleRemoveItem(selectedItemIndex)}
              className="bg-black text-red-500 border border-red-400 h-full text-lg font-black flex flex-col items-center justify-center"
            >
              <span>❌</span>
              <span>CANCEL</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Product Selection Modal - New Layout with Categories and Items */}
      <Dialog open={showProductModal} onOpenChange={setShowProductModal}>
        <DialogContent className="bg-black border-2 border-green-400 text-green-400 max-w-none w-[98vw] h-[95vh] overflow-hidden flex flex-col fixed top-[2.5vh] left-[1vw]">
          <DialogHeader className="flex-shrink-0 p-6 border-b-2 border-gray-600">
            <div className="flex justify-between items-center">
              <DialogTitle className="text-4xl font-black text-green-400">Select Item</DialogTitle>
              <Button
                onClick={() => setShowProductModal(false)}
                className="bg-black text-red-500 border-2 border-red-400 p-3 hover:bg-gray-900"
              >
                <X className="h-8 w-8" />
              </Button>
            </div>
          </DialogHeader>

          {/* Main Content - Split Layout */}
          <div className="flex-1 flex gap-6 overflow-hidden p-4">
            {/* Left Panel - Categories and Subcategories */}
            <div className="w-2/5 flex flex-col gap-6">
              {/* Categories Section */}
              <div className="flex-1 bg-gray-900 border-2 border-gray-600 rounded-lg">
                <div className="bg-green-400 text-black text-center py-3 font-black text-xl">
                  Category
                </div>
                <div className="p-4 max-h-80 overflow-y-auto">
                  {categories.map((category) => (
                    <div
                      key={category}
                      onClick={() => {
                        setSelectedCategory(category)
                        setSelectedSubcategory(null)
                      }}
                      className={`p-3 mb-2 cursor-pointer border-2 rounded-lg text-base font-bold transition-colors ${
                        selectedCategory === category
                          ? "bg-green-400 text-black border-green-400"
                          : "bg-black text-green-400 border-gray-600 hover:border-green-400 hover:bg-gray-800"
                      }`}
                    >
                      {category}
                    </div>
                  ))}
                </div>
              </div>

              {/* Subcategories Section */}
              <div className="flex-1 bg-gray-900 border-2 border-gray-600 rounded-lg">
                <div className="bg-green-400 text-black text-center py-3 font-black text-xl">
                  Sub Category
                </div>
                <div className="p-4 max-h-80 overflow-y-auto">
                  {selectedCategory && getSubcategoriesForCategory(selectedCategory).map((subcategory) => (
                    <div
                      key={subcategory}
                      onClick={() => setSelectedSubcategory(subcategory)}
                      className={`p-3 mb-2 cursor-pointer border-2 rounded-lg text-base font-bold transition-colors ${
                        selectedSubcategory === subcategory
                          ? "bg-green-400 text-black border-green-400"
                          : "bg-black text-green-400 border-gray-600 hover:border-green-400 hover:bg-gray-800"
                      }`}
                    >
                      {subcategory}
                    </div>
                  ))}
                  {selectedCategory && getSubcategoriesForCategory(selectedCategory).length === 0 && (
                    <div className="text-gray-500 text-base p-4">No subcategories</div>
                  )}
                </div>
              </div>
            </div>

            {/* Right Panel - Items List */}
            <div className="flex-1 bg-gray-900 border-2 border-gray-600 rounded-lg flex flex-col">
              <div className="bg-green-400 text-black text-center py-3 font-black text-xl">
                Items
              </div>
              <div className="flex-1 overflow-y-auto">
                {getFilteredProducts().map((product) => {
                  const quantityInCart = getItemQuantityInCart(product.name)
                  return (
                    <div
                      key={product.id}
                      className={`flex items-center justify-between p-4 border-b-2 border-gray-700 hover:bg-gray-800 transition-colors ${
                        quantityInCart > 0 ? "bg-gray-800" : ""
                      }`}
                    >
                      {/* Product Info */}
                      <div className="flex-1">
                        <div className="text-green-400 font-bold text-lg mb-2">
                          {product.name}
                        </div>
                        <div className="text-red-500 font-black text-xl mb-1">
                          ${formatCurrency(product.price)}
                        </div>
                        {product.subcategory && (
                          <div className="text-gray-400 text-sm">
                            {product.subcategory}
                          </div>
                        )}
                      </div>

                      {/* Quantity and Controls */}
                      <div className="flex items-center gap-3">
                        {quantityInCart > 0 && (
                          <div className="bg-green-400 text-black rounded-full w-8 h-8 flex items-center justify-center text-sm font-black">
                            {quantityInCart}
                          </div>
                        )}
                        <Button
                          onClick={() => handleRemoveItemFromModal(product)}
                          disabled={quantityInCart === 0}
                          className={`w-12 h-12 p-0 text-sm font-black ${
                            quantityInCart > 0
                              ? "bg-red-800 text-red-400 border-2 border-red-400 hover:bg-red-700"
                              : "bg-gray-800 text-gray-500 border-2 border-gray-600 cursor-not-allowed"
                          }`}
                        >
                          <Minus className="h-4 w-4" />
                        </Button>
                        <Button
                          onClick={() => handleAddItem(product)}
                          className="w-12 h-12 p-0 text-sm font-black bg-green-800 text-green-400 border-2 border-green-400 hover:bg-green-700"
                        >
                          <Check className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  )
                })}
                {getFilteredProducts().length === 0 && (
                  <div className="text-gray-500 text-center p-12 text-lg">
                    {selectedCategory ? "No items in this category" : "Select a category to view items"}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="flex-shrink-0 mt-6 pt-6 border-t-2 border-gray-600 mx-4">
            <div className="flex justify-between items-center">
              <div className="text-green-400 font-bold text-lg">
                Items in Cart: <span className="text-2xl font-black">{cartItems.length}</span>
              </div>
              <div className="text-green-400 font-bold text-lg">
                Total: <span className="text-2xl font-black">${formatCurrency(calculateTotal())}</span>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Checkout Success Modal */}
      <Dialog open={showCheckoutModal} onOpenChange={setShowCheckoutModal}>
        <DialogContent className="bg-black border-4 border-green-400 text-green-400 max-w-4xl w-[90vw] max-h-[90vh]">
          <DialogHeader>
            <DialogTitle className="text-6xl font-black text-green-400 text-center mb-8">SUCCESSFUL SALE!</DialogTitle>
          </DialogHeader>
          <div className="text-center space-y-8 py-8">
            {/* Main Total Amount */}
            <div className="bg-gray-900 border-2 border-green-400 rounded-lg p-8 mb-8">
              <div className="text-9xl font-black text-green-400 leading-none">${formatCurrency(calculateTotal())}</div>
            </div>

            {/* Sale Details */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="bg-gray-900 border border-green-400 rounded-lg p-6">
                <div className="text-2xl font-bold text-green-300 mb-2">Items Sold</div>
                <div className="text-5xl font-black text-green-400">{cartItems.length}</div>
              </div>
              <div className="bg-gray-900 border border-green-400 rounded-lg p-6">
                <div className="text-2xl font-bold text-green-300 mb-2">Subtotal</div>
                <div className="text-5xl font-black text-green-400">${formatCurrency(calculateSubtotal())}</div>
              </div>
              <div className="bg-gray-900 border border-green-400 rounded-lg p-6">
                <div className="text-2xl font-bold text-green-300 mb-2">Tax (6.625%)</div>
                <div className="text-5xl font-black text-green-400">${formatCurrency(calculateTax())}</div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-6 justify-center mt-12">
              <Button
                onClick={handleCompleteCheckout}
                className="bg-green-600 hover:bg-green-700 text-black border-2 border-green-400 text-3xl font-black px-12 py-6 h-auto"
              >
                NEW TRANSACTION
              </Button>
              <Button
                onClick={() => setShowCheckoutModal(false)}
                className="bg-red-600 hover:bg-red-700 text-white border-2 border-red-400 text-3xl font-black px-12 py-6 h-auto"
              >
                CLOSE
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
